import { schoolInfo } from './schoolData';

type Language = 'en' | 'id';

interface MatchResult {
  answer: string;
  score: number;
}

export async function getResponse(query: string, language: Language): Promise<string> {
  const cleanQuery = query.toLowerCase().trim();
  const data = schoolInfo[language];

  // Program-specific keywords mapping
  const programKeywords = {
    'elektronika': ['industrial_electronics', 'teknik_elektronika_industri'],
    'electronics': ['industrial_electronics', 'teknik_elektronika_industri'],
    'listrik': ['electrical_power', 'teknik_instalasi_tenaga_listrik'],
    'electrical': ['electrical_power', 'teknik_instalasi_tenaga_listrik'],
    'power': ['electrical_power', 'teknik_instalasi_tenaga_listrik'],
    'kendaraan': ['light_vehicle', 'teknik_kendaraan_ringan'],
    'vehicle': ['light_vehicle', 'teknik_kendaraan_ringan'],
    'automotive': ['light_vehicle', 'teknik_kendaraan_ringan'],
    'otomotif': ['light_vehicle', 'teknik_kendaraan_ringan'],
    'akuntansi': ['accounting', 'akuntansi'],
    'accounting': ['accounting', 'akuntansi'],
    'permesinan': ['mechanical_engineering', 'teknik_permesinan'],
    'mechanical': ['mechanical_engineering', 'teknik_permesinan'],
    'mesin': ['mechanical_engineering', 'teknik_permesinan'],
    'perhotelan': ['hospitality', 'perhotelan'],
    'hospitality': ['hospitality', 'perhotelan'],
    'hotel': ['hospitality', 'perhotelan'],
    'sepeda motor': ['motorcycle_engineering', 'teknik_sepeda_motor'],
    'motorcycle': ['motorcycle_engineering', 'teknik_sepeda_motor'],
    'motor': ['motorcycle_engineering', 'teknik_sepeda_motor'],
    'kimia': ['industrial_chemistry', 'teknik_kimia_industri'],
    'chemistry': ['industrial_chemistry', 'teknik_kimia_industri'],
    'chemical': ['industrial_chemistry', 'teknik_kimia_industri']
  };

  // Check for program list queries
  if (cleanQuery.includes('program') && (cleanQuery.includes('keahlian') || cleanQuery.includes('jurusan') || cleanQuery.includes('tersedia') || cleanQuery.includes('available') || cleanQuery.includes('majors'))) {
    if (data.programs && typeof data.programs === 'object' && data.programs.majors) {
      return data.programs.overview + '\n\n' + data.programs.majors;
    }
  }

  // Check for advantages/keunggulan queries
  if (cleanQuery.includes('keunggulan') || cleanQuery.includes('advantages') || cleanQuery.includes('unggulan') || cleanQuery.includes('kelebihan')) {
    if (data.advantages && typeof data.advantages === 'object') {
      let response = data.advantages.overview + '\n\n';
      response += '1. ' + data.advantages.hrd_initiated + '\n\n';
      response += '2. ' + data.advantages.strategic_location + '\n\n';
      response += '3. ' + data.advantages.integrated_system + '\n\n';
      response += '4. ' + data.advantages.industrial_culture + '\n\n';
      response += '5. ' + data.advantages.partnerships;
      return response;
    }
  }

  // Check for international program queries (Japan and Germany)
  if ((cleanQuery.includes('jepang') || cleanQuery.includes('japan') ||
       cleanQuery.includes('jerman') || cleanQuery.includes('germany') ||
       cleanQuery.includes('ausbildung')) &&
      (cleanQuery.includes('magang') || cleanQuery.includes('peluang') ||
       cleanQuery.includes('program') || cleanQuery.includes('internship'))) {
    if (data.bmw_plus_program && typeof data.bmw_plus_program === 'object') {
      return data.bmw_plus_program.international_path + '\n\n' + data.bmw_plus_program.dynamic_learning;
    }
  }

  // Check for BMW+ program queries
  if (cleanQuery.includes('bmw') || cleanQuery.includes('BMW') ||
      (cleanQuery.includes('program') && cleanQuery.includes('peminatan'))) {
    if (data.bmw_plus_program && typeof data.bmw_plus_program === 'object') {
      let response = data.bmw_plus_program.overview + '\n\n';
      response += '🔹 ' + data.bmw_plus_program.work_path + '\n\n';
      response += '🔹 ' + data.bmw_plus_program.education_path + '\n\n';
      response += '🔹 ' + data.bmw_plus_program.entrepreneurship_path + '\n\n';
      response += '🔹 ' + data.bmw_plus_program.international_path + '\n\n';
      response += data.bmw_plus_program.dynamic_learning;
      return response;
    }
  }

  // Check for advantages queries
  if (cleanQuery.includes('keunggulan') || cleanQuery.includes('advantage') ||
      (cleanQuery.includes('5') && (cleanQuery.includes('keunggulan') || cleanQuery.includes('advantage')))) {
    if (data.advantages && typeof data.advantages === 'object') {
      let response = data.advantages.overview + '\n\n';
      response += '1️⃣ ' + data.advantages.hrd_partnership + '\n\n';
      response += '2️⃣ ' + data.advantages.strategic_location + '\n\n';
      response += '3️⃣ ' + data.advantages.link_and_match + '\n\n';
      response += '4️⃣ ' + data.advantages.industrial_culture + '\n\n';
      response += '5️⃣ ' + data.advantages.collaboration;
      return response;
    }
  }

  // Check for communication triangle queries
  if (cleanQuery.includes('segitiga') || cleanQuery.includes('komunikasi') ||
      cleanQuery.includes('triangle') || cleanQuery.includes('communication')) {
    if (data.communication_triangle && typeof data.communication_triangle === 'object') {
      let response = data.communication_triangle.overview + '\n\n';
      response += '👨‍🎓 ' + data.communication_triangle.student + '\n\n';
      response += '🏫 ' + data.communication_triangle.school + '\n\n';
      response += '👨‍👩‍👧‍👦 ' + data.communication_triangle.parents + '\n\n';
      response += '✨ ' + data.communication_triangle.success_formula;
      return response;
    }
  }

  // Check for link and match system queries
  if (cleanQuery.includes('link') && cleanQuery.includes('match') ||
      cleanQuery.includes('sistem') && (cleanQuery.includes('link') || cleanQuery.includes('match'))) {
    if (data.advantages && typeof data.advantages === 'object') {
      return data.advantages.link_and_match;
    }
  }

  // Check for industry partnerships queries
  if (cleanQuery.includes('kerjasama') || cleanQuery.includes('partnership') || cleanQuery.includes('industri') ||
      cleanQuery.includes('perusahaan') || cleanQuery.includes('company') || cleanQuery.includes('mitra') ||
      cleanQuery.includes('sponsor') || cleanQuery.includes('magang') || cleanQuery.includes('internship') ||
      cleanQuery.includes('pkl') || cleanQuery.includes('kerja') && cleanQuery.includes('sama')) {

    if (data.industry_partnerships && typeof data.industry_partnerships === 'object') {
      // Check for specific aspects of partnerships
      if (cleanQuery.includes('magang') || cleanQuery.includes('internship') || cleanQuery.includes('pkl')) {
        return data.industry_partnerships.internship_program;
      } else if (cleanQuery.includes('kerja') || cleanQuery.includes('job') || cleanQuery.includes('karir') || cleanQuery.includes('career')) {
        return data.industry_partnerships.job_placement;
      } else if (cleanQuery.includes('manfaat') || cleanQuery.includes('benefit')) {
        return data.industry_partnerships.benefits;
      } else if (cleanQuery.includes('perusahaan') || cleanQuery.includes('company') || cleanQuery.includes('nama')) {
        return data.industry_partnerships.major_partners;
      } else {
        // General partnership information
        let response = data.industry_partnerships.overview + '\n\n';
        response += data.industry_partnerships.benefits + '\n\n';
        response += 'Perusahaan Mitra Utama:\n' + data.industry_partnerships.major_partners;
        return response;
      }
    }
  }

  // Check for specific school information queries first
  if (cleanQuery.includes('profil') || cleanQuery.includes('profile')) {
    return data.profile || data.overview;
  }

  // Check for history/founding queries
  if (cleanQuery.includes('sejarah') || cleanQuery.includes('history') ||
      cleanQuery.includes('didirikan') || cleanQuery.includes('founded') ||
      cleanQuery.includes('berdiri') || cleanQuery.includes('established') ||
      cleanQuery.includes('yayasan') || cleanQuery.includes('foundation')) {
    return data.history;
  }

  // Check for school information queries
  if (data.school_info && typeof data.school_info === 'object') {
    if (cleanQuery.includes('akreditasi') || cleanQuery.includes('accreditation')) {
      return `Akreditasi SMK Mitra Industri MM2100: ${data.school_info.akreditasi}`;
    }
    if (cleanQuery.includes('kepala') && cleanQuery.includes('sekolah') ||
        cleanQuery.includes('principal') || cleanQuery.includes('headmaster')) {
      return `Kepala Sekolah: ${data.school_info.kepala_sekolah}`;
    }
    if (cleanQuery.includes('alamat') || cleanQuery.includes('address') ||
        cleanQuery.includes('lokasi') || cleanQuery.includes('location')) {
      return `Alamat: ${data.school_info.alamat}`;
    }
    if (cleanQuery.includes('website') || cleanQuery.includes('situs') ||
        cleanQuery.includes('web') || cleanQuery.includes('url')) {
      return `Situs web: ${data.school_info.situs_web}`;
    }
    if (cleanQuery.includes('email') || cleanQuery.includes('kontak')) {
      return `Email: ${data.school_info.email}`;
    }
    if (cleanQuery.includes('kurikulum') || cleanQuery.includes('curriculum')) {
      return `Kurikulum yang digunakan: ${data.school_info.kurikulum}`;
    }
    if (cleanQuery.includes('kampus') || cleanQuery.includes('campus') ||
        cleanQuery.includes('cabang') || cleanQuery.includes('branch')) {
      return `Kampus: ${data.school_info.kampus}`;
    }
    if (cleanQuery.includes('afiliasi') || cleanQuery.includes('affiliation') ||
        cleanQuery.includes('kerjasama') && cleanQuery.includes('sekolah')) {
      return `Sekolah Afiliasi: ${data.school_info.afiliasi}`;
    }
    if (cleanQuery.includes('motto') || cleanQuery.includes('moto')) {
      return `Motto: ${data.school_info.motto}`;
    }
    if (cleanQuery.includes('jenis') || cleanQuery.includes('type') ||
        cleanQuery.includes('status') && cleanQuery.includes('sekolah')) {
      return `Jenis sekolah: ${data.school_info.jenis}`;
    }
    if (cleanQuery.includes('kelas') || cleanQuery.includes('tingkat') ||
        cleanQuery.includes('grade') || cleanQuery.includes('level')) {
      return `Rentang kelas: ${data.school_info.rentang_kelas}`;
    }
  }

  if (cleanQuery.includes('tagline') || cleanQuery.includes('slogan')) {
    return data.tagline || (language === 'id'
      ? "Reach Your Success with Strong Academic Vocational & Social Skill - Raih sukses anda dengan keterampilan kejuruan dan sosial akademik yang kuat yang berlandaskan pada nilai kejujuran, bertanggung jawab, disiplin, kerjasama dan peduli pada sesama dan lingkungan."
      : "Reach Your Success with Strong Academic Vocational & Social Skill");
  }

  if (cleanQuery.includes('visi') || cleanQuery.includes('vision')) {
    return data.vision;
  }

  if (cleanQuery.includes('misi') || cleanQuery.includes('mission')) {
    if (data.mission && typeof data.mission === 'object') {
      return data.mission.values;
    }
    return data.mission;
  }

  if ((cleanQuery.includes('visi') && cleanQuery.includes('misi')) ||
      (cleanQuery.includes('vision') && cleanQuery.includes('mission'))) {
    let response = language === 'id' ? 'VISI:\n' : 'VISION:\n';
    response += data.vision + '\n\n';
    response += language === 'id' ? 'MISI:\n' : 'MISSION:\n';
    if (data.mission && typeof data.mission === 'object') {
      response += data.mission.values;
    } else {
      response += data.mission;
    }
    return response;
  }

  // Check for program-specific queries
  for (const keyword in programKeywords) {
    if (cleanQuery.includes(keyword)) {
      const programKeys = programKeywords[keyword as keyof typeof programKeywords];
      for (const programKey of programKeys) {
        if (data.programs && typeof data.programs === 'object' && data.programs[programKey as keyof typeof data.programs]) {
          const program = data.programs[programKey as keyof typeof data.programs];
          if (typeof program === 'object' && program !== null) {
            // Check what specific information is being asked
            if (cleanQuery.includes('karir') || cleanQuery.includes('career') || cleanQuery.includes('kerja') || cleanQuery.includes('job')) {
              return `${program.name}:\n\n${program.career_prospects}`;
            } else if (cleanQuery.includes('fasilitas') || cleanQuery.includes('facilities') || cleanQuery.includes('lab')) {
              return `${program.name}:\n\n${program.facilities}`;
            } else {
              return `${program.name}:\n\n${program.description}\n\nProspek Karir/Career Prospects:\n${program.career_prospects}`;
            }
          }
        }
      }
    }
  }

  // Check for values-specific queries
  if (cleanQuery.includes('nilai') || cleanQuery.includes('value') ||
      cleanQuery.includes('jujur') || cleanQuery.includes('honest') ||
      cleanQuery.includes('tanggung jawab') || cleanQuery.includes('responsibility') ||
      cleanQuery.includes('disiplin') || cleanQuery.includes('discipline') ||
      cleanQuery.includes('kerja sama') || cleanQuery.includes('teamwork') ||
      cleanQuery.includes('peduli') || cleanQuery.includes('care')) {

    const values = data.values;
    if (typeof values === 'object' && values !== null) {
      // Check for specific value queries
      if (cleanQuery.includes('jujur') || cleanQuery.includes('honest')) {
        return values.jujur || values.honesty || values.overview;
      }
      if (cleanQuery.includes('tanggung jawab') || cleanQuery.includes('responsibility')) {
        return values.tanggung_jawab || values.responsibility || values.overview;
      }
      if (cleanQuery.includes('disiplin') || cleanQuery.includes('discipline')) {
        return values.disiplin || values.discipline || values.overview;
      }
      if (cleanQuery.includes('kerja sama') || cleanQuery.includes('teamwork')) {
        return values.kerja_sama || values.teamwork || values.overview;
      }
      if (cleanQuery.includes('peduli') || cleanQuery.includes('care')) {
        return values.peduli || values.care || values.overview;
      }
      // Return overview for general values query
      if (values.overview) {
        const allValues = [];
        if (values.jujur) allValues.push(values.jujur);
        if (values.honesty) allValues.push(values.honesty);
        if (values.tanggung_jawab) allValues.push(values.tanggung_jawab);
        if (values.responsibility) allValues.push(values.responsibility);
        if (values.disiplin) allValues.push(values.disiplin);
        if (values.discipline) allValues.push(values.discipline);
        if (values.kerja_sama) allValues.push(values.kerja_sama);
        if (values.teamwork) allValues.push(values.teamwork);
        if (values.peduli) allValues.push(values.peduli);
        if (values.care) allValues.push(values.care);

        return values.overview + '\n\n' + allValues.join('\n\n');
      }
    }
  }

  // Check for extracurricular activities queries
  if (data.ekstrakurikuler && typeof data.ekstrakurikuler === 'object') {
    if (cleanQuery.includes('ekstrakurikuler') || cleanQuery.includes('extracurricular') ||
        (cleanQuery.includes('kegiatan') && (cleanQuery.includes('siswa') || cleanQuery.includes('sekolah')))) {
      return data.ekstrakurikuler.all_activities;
    }
    if (cleanQuery.includes('club') || cleanQuery.includes('klub')) {
      return `Klub yang tersedia: ${data.ekstrakurikuler.clubs}`;
    }
    if (cleanQuery.includes('olahraga') || cleanQuery.includes('sport')) {
      return `Kegiatan olahraga: ${data.ekstrakurikuler.sports}`;
    }
    if (cleanQuery.includes('seni') || cleanQuery.includes('art') || cleanQuery.includes('musik')) {
      return `Kegiatan seni: ${data.ekstrakurikuler.arts}`;
    }
    if (cleanQuery.includes('esport') || cleanQuery.includes('e-sport') || cleanQuery.includes('gaming')) {
      return "SMK Mitra Industri MM2100 memiliki ekstrakurikuler Esport untuk mengembangkan kemampuan gaming dan teknologi digital siswa.";
    }
  }

  // Check for direct matches
  for (const category in data) {
    // Skip if not a direct property of the object
    if (!Object.prototype.hasOwnProperty.call(data, category)) continue;

    const categoryData = data[category as keyof typeof data];

    // Handle both string and object types
    if (typeof categoryData === 'string') {
      if (cleanQuery.includes(category.toLowerCase())) {
        return categoryData;
      }
    } else if (typeof categoryData === 'object' && categoryData !== null) {
      for (const key in categoryData) {
        if (cleanQuery.includes(key.toLowerCase())) {
          return categoryData[key];
        }
      }
    }
  }
  
  // If no direct match, try fuzzy matching to find the most relevant response
  const matches: MatchResult[] = [];

  // Special handling for program-related queries (but not BMW+ program)
  if ((cleanQuery.includes('program') || cleanQuery.includes('jurusan') || cleanQuery.includes('keahlian')) &&
      !cleanQuery.includes('bmw') && !cleanQuery.includes('BMW') && !cleanQuery.includes('peminatan')) {
    if (data.programs && typeof data.programs === 'object') {
      matches.push({
        answer: data.programs.overview + '\n\n' + data.programs.majors,
        score: 10
      });
    }
  }

  for (const category in data) {
    if (!Object.prototype.hasOwnProperty.call(data, category)) continue;

    const categoryData = data[category as keyof typeof data];
    const keyWords = category.toLowerCase().split(' ');

    // Calculate match score based on how many keywords from the category match the query
    let score = 0;
    keyWords.forEach(word => {
      if (cleanQuery.includes(word) && word.length > 2) { // Only count meaningful words
        score += 1;
      }
    });

    if (score > 0) {
      if (typeof categoryData === 'string') {
        matches.push({
          answer: categoryData,
          score: score
        });
      } else if (typeof categoryData === 'object' && categoryData !== null) {
        for (const key in categoryData) {
          const subKeyWords = key.toLowerCase().split(' ');
          let subScore = score;

          subKeyWords.forEach(word => {
            if (cleanQuery.includes(word) && word.length > 2) {
              subScore += 1;
            }
          });

          if (subScore > 0) {
            matches.push({
              answer: categoryData[key],
              score: subScore
            });
          }
        }
      }
    }
  }
  
  // Sort matches by score (highest first)
  matches.sort((a, b) => b.score - a.score);
  
  // Return the best match or a default response
  if (matches.length > 0) {
    return matches[0].answer;
  }
  
  // Default response if no matches found
  return language === 'id' 
    ? "Maaf, saya tidak memiliki informasi tentang pertanyaan tersebut. Silakan tanyakan hal lain seputar SMK Mitra Industri MM2100."
    : "I'm sorry, I don't have information about that question. Please ask something else about SMK Mitra Industri MM2100.";
}