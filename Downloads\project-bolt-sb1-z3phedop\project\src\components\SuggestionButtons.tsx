import React from 'react';
import { useLanguage } from '../context/LanguageContext';

interface SuggestionButtonsProps {
  onSendSuggestion: (text: string) => void;
}

const SuggestionButtons: React.FC<SuggestionButtonsProps> = ({ onSendSuggestion }) => {
  const { language } = useLanguage();

  const suggestions = language === 'id'
    ? [
      'Apa 5 keunggulan SMK Mitra Industri MM2100?',
      'Ceritakan tentang Program BMW+',
      'Apa itu Segitiga Emas Komunikasi?',
      'Program keahlian apa saja yang tersedia?',
      'Bagaimana sistem link and match?',
      'Apa peluang magang ke Jepang dan Jerman?'
    ]
    : [
      'What are the 5 advantages of SMK Mitra Industri MM2100?',
      'Tell me about the BMW+ Program',
      'What is the Golden Triangle of Communication?',
      'What study programs are available?',
      'How does the link and match system work?',
      'What are the internship opportunities in Japan and Germany?'
    ];

  return (
    <div>
      <p className="text-sm text-gray-400 mb-2">
        {language === 'id' ? 'Pertanyaan umum:' : 'Common questions:'}
      </p>
      <div className="flex flex-wrap gap-2">
        {suggestions.map((suggestion, index) => (
          <button
            key={index}
            className="suggestion-btn text-white text-sm py-2 px-4 rounded-xl"
            onClick={() => onSendSuggestion(suggestion)}
          >
            {suggestion}
          </button>
        ))}
      </div>
    </div>
  );
};

export default SuggestionButtons;